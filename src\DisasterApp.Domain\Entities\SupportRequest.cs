﻿using System;
using System.Collections.Generic;

namespace DisasterApp.Domain.Entities;

public partial class SupportRequest
{
    public int Id { get; set; }

    public Guid ReportId { get; set; }

    public string Description { get; set; } = null!;

    public int? QuantityNeeded { get; set; }

    public int? QuantityReceived { get; set; }

    public byte Urgency { get; set; }

    public byte? Priority { get; set; }

    public DateTime? Deadline { get; set; }

    public byte? Status { get; set; }

    public Guid UserId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public int SupportTypeId { get; set; }

    public virtual ICollection<AssistanceProvided> AssistanceProvideds { get; set; } = new List<AssistanceProvided>();

    public virtual DisasterReport Report { get; set; } = null!;

    public virtual SupportType SupportType { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
