﻿using DisasterApp.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IDisasterEventService
    {
        Task<IEnumerable<DisasterEventDto>> GetAllAsync();
        Task<DisasterEventDto?> GetByIdAsync(Guid id);
        Task<DisasterEventDto>  CreateAsync(CreateDisasterEventDto dto);
        Task UpdateAsync(Guid id,UpdateDisasterEventDto dto);
        Task DeleteAsync(Guid id);
    }
}
