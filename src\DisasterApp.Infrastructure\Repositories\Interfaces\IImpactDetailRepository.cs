﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IImpactDetailRepository
    {
        Task<IEnumerable<ImpactDetail>> GetAllAsync();
        Task<ImpactDetail?> GetByIdAsync(int id);
        Task AddAsync(ImpactDetail entity);
        Task UpdateAsync(ImpactDetail entity);
        Task DeleteAsync(int id);
    }
}
