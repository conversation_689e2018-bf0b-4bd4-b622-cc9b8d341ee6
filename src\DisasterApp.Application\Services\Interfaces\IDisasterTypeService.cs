﻿using DisasterApp.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IDisasterTypeService
    {
        Task<IEnumerable<DisasterTypeDto>> GetAllAsync();
        Task<DisasterTypeDto?> GetByIdAsync(int id);
        Task<DisasterTypeDto> CreateAsync(CreateDisasterTypeDto createDto);
        Task<DisasterTypeDto?> UpdateAsync(int id,UpdateDisasterTypeDto updateDto);
        Task<bool> DeleteAsync(int id);
    }
}
