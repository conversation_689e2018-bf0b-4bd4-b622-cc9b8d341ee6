﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IDisasterTypeRepository
    {
        Task<IEnumerable<DisasterType>> GetAllAsync();
        Task<DisasterType?> GetByIdAsync(int id);
        Task AddAsync(DisasterType disasterType);
        Task UpdateAsync(DisasterType disasterType);       // ✅ New
        Task<bool> DeleteAsync(int id);

    }
}
