﻿using DisasterApp.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IDisasterReportService
    {
        Task<IEnumerable<DisasterReportDto>> GetAllAsync();
        Task<DisasterReportDto?> GetByIdAsync(Guid id);
        Task<DisasterReportDto> CreateAsync(CreateDisasterReportDto dto);
        Task UpdateAsync(Guid id, CreateDisasterReportDto dto);
        Task DeleteAsync(Guid id);
    }
}
