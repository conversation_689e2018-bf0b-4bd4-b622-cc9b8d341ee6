﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.DTOs
{
    public class CreateDisasterEventDto
    {
        public string Name { get; set; } = null!;
        public int DisasterTypeId { get; set; }

    }
    public class DisasterEventDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public int DisasterTypeId { get; set; }
        public string DisasterTypeName { get; set; } = null!;
    }
    public class UpdateDisasterEventDto
    {
        public string Name { get; set; } = null!;
        public int DisasterTypeId { get; set; }
    }
}
