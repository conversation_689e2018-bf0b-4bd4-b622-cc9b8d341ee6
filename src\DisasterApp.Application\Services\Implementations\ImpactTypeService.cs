﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public class ImpactTypeService : IImpactTypeService
    {
        private readonly IImpactTypeRepository _repository;
        public ImpactTypeService(IImpactTypeRepository repository)
        {
            _repository = repository;
        }
        public async Task CreateAsync(CreateImactTypeDto dto)
        {
            var entity = new ImpactType
            {
                Name = dto.Name,
                Description = dto.Description
            };

            await _repository.AddAsync(entity);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<IEnumerable<ImpactTypeDto>> GetAllAsync()
        {
            var types = await _repository.GetAllAsync();
            return types.Select(t => new ImpactTypeDto
            {
                Id = t.Id,
                Name = t.Name,
                Description = t.Description
            });
        }

        public async Task<ImpactTypeDto?> GetByIdAsync(int id)
        {
            var t = await _repository.GetByIdAsync(id);
            return t == null ? null : new ImpactTypeDto
            {
                Id = t.Id,
                Name = t.Name,
                Description = t.Description
            };
        }

        public async Task UpdateAsync(int id, UpdateImpactTypeDto dto)
        {
            var existing = await _repository.GetByIdAsync(id);
            if (existing == null) return;

            existing.Name = dto.Name;
            existing.Description = dto.Description;

            await _repository.UpdateAsync(existing);
        }
    }
}
