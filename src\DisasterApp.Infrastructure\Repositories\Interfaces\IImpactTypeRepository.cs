﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IImpactTypeRepository
    {
        Task<IEnumerable<ImpactType>> GetAllAsync();
        Task<ImpactType?> GetByIdAsync(int id);
        Task AddAsync(ImpactType entity);
        Task UpdateAsync(ImpactType entity);
        Task DeleteAsync(int id);
    }
}
