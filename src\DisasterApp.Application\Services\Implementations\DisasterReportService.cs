﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services.Implementations
{
    public class DisasterReportService : IDisasterReportService
    {
        private readonly IDisasterReportRepository _repository;
        public DisasterReportService ( IDisasterReportRepository repository)
        {
            _repository = repository;
        }
        public async Task<DisasterReportDto> CreateAsync(CreateDisasterReportDto dto)
        {
            var entity = new DisasterReport
            {
                Id = Guid.NewGuid(),
                Title = dto.Title,
                Description = dto.Description,
                Timestamp = dto.Timestamp,
                Severity = dto.Severity,
                Status = dto.Status,
                EstimatedAffected = dto.EstimatedAffected,
                UserId = dto.UserId,
                DisasterEventId = dto.DisasterEventId,
                CreatedAt = DateTime.UtcNow
            };

            var created = await _repository.AddAsync(entity);

            return new DisasterReportDto
            {
                Id = created.Id,
                Title = created.Title,
                Description = created.Description,
                Timestamp = created.Timestamp,
                Severity = created.Severity,
                Status = created.Status,
                EstimatedAffected = created.EstimatedAffected,
                VerifiedBy = created.VerifiedBy,
                VerifiedAt = created.VerifiedAt,
                IsDeleted = created.IsDeleted,
                UserId = created.UserId,
                CreatedAt = created.CreatedAt,
                UpdatedAt = created.UpdatedAt,
                DisasterEventId = created.DisasterEventId
            };
        }

        public async Task DeleteAsync(Guid id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<IEnumerable<DisasterReportDto>> GetAllAsync()
        {
            var reports = await _repository.GetAllAsync();

            return reports.Select(r => new DisasterReportDto
            {
                Id = r.Id,
                Title = r.Title,
                Description = r.Description,
                Timestamp = r.Timestamp,
                Severity = r.Severity,
                Status = r.Status,
                EstimatedAffected = r.EstimatedAffected,
                VerifiedBy = r.VerifiedBy,
                VerifiedAt = r.VerifiedAt,
                IsDeleted = r.IsDeleted,
                UserId = r.UserId,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt,
                DisasterEventId = r.DisasterEventId
            });
        }

        public async Task<DisasterReportDto?> GetByIdAsync(Guid id)
        {
            var r = await _repository.GetByIdAsync(id);
            if (r == null) return null;

            return new DisasterReportDto
            {
                Id = r.Id,
                Title = r.Title,
                Description = r.Description,
                Timestamp = r.Timestamp,
                Severity = r.Severity,
                Status = r.Status,
                EstimatedAffected = r.EstimatedAffected,
                VerifiedBy = r.VerifiedBy,
                VerifiedAt = r.VerifiedAt,
                IsDeleted = r.IsDeleted,
                UserId = r.UserId,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt,
                DisasterEventId = r.DisasterEventId
            };
        }

        public async Task UpdateAsync(Guid id, CreateDisasterReportDto dto)
        {
            var entity = await _repository.GetByIdAsync(id);
            if (entity == null) throw new KeyNotFoundException("Disaster report not found.");

            entity.Title = dto.Title;
            entity.Description = dto.Description;
            entity.Timestamp = dto.Timestamp;
            entity.Severity = dto.Severity;
            entity.Status = dto.Status;
            entity.EstimatedAffected = dto.EstimatedAffected;
            entity.UserId = dto.UserId;
            entity.DisasterEventId = dto.DisasterEventId;
            entity.UpdatedAt = DateTime.UtcNow;

            await _repository.UpdateAsync(entity);
        }

    }
}
