﻿using DisasterApp.Application.DTOs;
using DisasterApp.Application.Services;
using DisasterApp.Application.Services.Implementations;
using DisasterApp.Infrastructure.Repositories;
using Microsoft.AspNetCore.Mvc;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace DisasterApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DisasterReportController : ControllerBase
    {
        private readonly IDisasterReportService _service;
        public DisasterReportController(IDisasterReportService service)
        {
            _service = service;
        }

        // GET: api/<DisasterReportController>
        [HttpGet]
        public async Task<IActionResult> GetAll() =>
        Ok(await _service.GetAllAsync());

        // GET api/<DisasterReportController>/5
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var report = await _service.GetByIdAsync(id);
            if (report == null) return NotFound();
            return Ok(report);
        }

        [HttpPost]
        public async Task<IActionResult> Create(CreateDisasterReportDto dto)
        {
            var created = await _service.CreateAsync(dto);
            return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, CreateDisasterReportDto dto)
        {
            await _service.UpdateAsync(id, dto);
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _service.DeleteAsync(id);
            return NoContent();
        }
    }
}
