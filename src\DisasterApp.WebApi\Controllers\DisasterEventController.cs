﻿using DisasterApp.Application.DTOs;
using DisasterApp.Application.Services;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace DisasterApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DisasterEventController : ControllerBase
    {
        private readonly IDisasterEventService _service;
        public DisasterEventController(IDisasterEventService service)
        {
            _service = service;
        }
       
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var events = await _service.GetAllAsync();
            return Ok(events);
        }


     
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _service.GetByIdAsync(id);
            if (result == null) return NotFound();
            return Ok(result);
        }

      
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateDisasterEventDto dto)
        {
            try
            {
                var createdEvent = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(GetById), new { id = createdEvent.Id }, createdEvent);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateDisasterEventDto dto)
        {
            try
            {
                await _service.UpdateAsync(id, dto);
                return NoContent(); // 204 - Successful but no body returned
            }
            catch (Exception ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _service.DeleteAsync(id);
            return Ok();
        }
    }
}
