﻿using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Data;
using DisasterApp.Infrastructure.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public class ImpactTypeRepository : IImpactTypeRepository
    {
        private readonly DisasterDbContext _context;
        public ImpactTypeRepository(DisasterDbContext context)
        {
            _context = context ;
        }
        public async Task AddAsync(ImpactType entity)
        {
            _context.ImpactTypes.Add(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var existing = await _context.ImpactTypes.FindAsync(id);
            if (existing != null)
            {
                _context.ImpactTypes.Remove(existing);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<ImpactType>> GetAllAsync()
        {
            return await _context.ImpactTypes.ToListAsync();
        }

        public async Task<ImpactType?> GetByIdAsync(int id)
        {
            return await _context.ImpactTypes.FindAsync(id);
        }

        public async Task UpdateAsync(ImpactType entity)
        {
            _context.ImpactTypes.Update(entity);
            await _context.SaveChangesAsync();
        }
    }
}
