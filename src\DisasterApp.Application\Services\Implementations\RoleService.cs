using DisasterApp.Application.Services.Interfaces;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DisasterApp.Application.Services.Implementations;

public class RoleService : IRoleService
{
    private readonly DisasterDbContext _context;
    private readonly ILogger<RoleService> _logger;
    private const string DefaultRoleName = "user";

    public RoleService(DisasterDbContext context, ILogger<RoleService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Role>> GetAllRolesAsync()
    {
        return await _context.Roles.ToListAsync();
    }

    public async Task<Role?> GetRoleByNameAsync(string roleName)
    {
        return await _context.Roles
            .FirstOrDefaultAsync(r => r.Name.ToLower() == roleName.ToLower());
    }

    public async Task<Role?> GetDefaultRoleAsync()
    {
        return await GetRoleByNameAsync(DefaultRoleName);
    }

    public async Task AssignRoleToUserAsync(Guid userId, string roleName)
    {
        var user = await _context.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.UserId == userId);

        if (user == null)
        {
            _logger.LogWarning("User with ID {UserId} not found", userId);
            throw new ArgumentException($"User with ID {userId} not found");
        }

        var role = await GetRoleByNameAsync(roleName);
        if (role == null)
        {
            _logger.LogWarning("Role {RoleName} not found", roleName);
            throw new ArgumentException($"Role {roleName} not found");
        }

        if (!user.Roles.Any(r => r.RoleId == role.RoleId))
        {
            user.Roles.Add(role);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Assigned role {RoleName} to user {UserId}", roleName, userId);
        }
        else
        {
            _logger.LogInformation("User {UserId} already has role {RoleName}", userId, roleName);
        }
    }

    public async Task AssignDefaultRoleToUserAsync(Guid userId)
    {
        await AssignRoleToUserAsync(userId, DefaultRoleName);
    }

    public async Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId)
    {
        var user = await _context.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.UserId == userId);

        return user?.Roles ?? new List<Role>();
    }

    public async Task<bool> UserHasRoleAsync(Guid userId, string roleName)
    {
        var user = await _context.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.UserId == userId);

        return user?.Roles.Any(r => r.Name.ToLower() == roleName.ToLower()) ?? false;
    }

    public async Task RemoveRoleFromUserAsync(Guid userId, string roleName)
    {
        var user = await _context.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.UserId == userId);

        if (user == null)
        {
            _logger.LogWarning("User with ID {UserId} not found", userId);
            throw new ArgumentException($"User with ID {userId} not found");
        }

        var role = user.Roles.FirstOrDefault(r => r.Name.ToLower() == roleName.ToLower());
        if (role != null)
        {
            user.Roles.Remove(role);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Removed role {RoleName} from user {UserId}", roleName, userId);
        }
        else
        {
            _logger.LogInformation("User {UserId} does not have role {RoleName}", userId, roleName);
        }
    }
}