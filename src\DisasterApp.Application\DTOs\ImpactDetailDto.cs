﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.DTOs

   {
        public class CreateImpactDetailDto
    {
        public Guid ReportId { get; set; }
        public string Description { get; set; } = null!;
        public byte? Severity { get; set; }
        public bool? IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public int ImpactTypeId { get; set; }
    }

    public class ImpactDetailDto
    {
        public int Id { get; set; }
        public Guid ReportId { get; set; }
        public string Description { get; set; } = null!;
        public byte? Severity { get; set; }
        public bool? IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public int ImpactTypeId { get; set; }
        public string ImpactTypeName { get; set; } = null!;
    }

    public class UpdateImpactDetailDto
    {
        public int Id { get; set; }
        public Guid ReportId { get; set; }
        public string Description { get; set; } = null!;
        public byte? Severity { get; set; }
        public bool? IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public int ImpactTypeId { get; set; }
    }
}


   
