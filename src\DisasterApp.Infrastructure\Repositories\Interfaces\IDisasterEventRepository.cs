﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IDisasterEventRepository
    {
        Task<IEnumerable<DisasterEvent>> GetAllAsync();
        Task<DisasterEvent?> GetByIdAsync(Guid id);
        Task AddAsync(DisasterEvent disasterEvent);
        Task UpdateAsync(Guid id,DisasterEvent disasterEvent);
        Task<DisasterEvent?> FindByNameAsync(string name);
        Task<bool> DeleteAsync(Guid id);
    }
}
