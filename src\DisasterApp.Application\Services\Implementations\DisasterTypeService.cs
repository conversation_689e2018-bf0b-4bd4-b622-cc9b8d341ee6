﻿using DisasterApp.Application.DTOs;
using DisasterApp.Application.Services.Interfaces;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public class DisasterTypeService : IDisasterTypeService
    {
        private readonly IDisasterTypeRepository _repository;
        public DisasterTypeService(IDisasterTypeRepository repository)
        {
            _repository = repository;
        }
        public async Task<IEnumerable<DisasterTypeDto>> GetAllAsync()
        {
            var disasterTypes=await _repository.GetAllAsync();
            return disasterTypes.Select(dt=>new DisasterTypeDto
            {
                Id = dt.Id,
                Name = dt.Name,
                IconName = dt.IconName,
                OrganizationId = dt.OrganizationId,
                Category = dt.Category
            }).ToList();
        }
        public async Task<DisasterTypeDto> CreateAsync(CreateDisasterTypeDto disasterTypeDto)
        {
            var disasterType = new DisasterType
            {
                Name = disasterTypeDto.Name,
                IconName = disasterTypeDto.IconName,
                OrganizationId = disasterTypeDto.OrganizationId,
                Category = disasterTypeDto.Category
            };
            await _repository.AddAsync(disasterType);
            return new DisasterTypeDto
            {
                Id = disasterType.Id,
                Name = disasterType.Name,
                IconName = disasterType.IconName,
                OrganizationId = disasterType.OrganizationId,
                Category = disasterType.Category
            };
        }

        public async Task<bool> DeleteAsync(int id)
        {
            return await _repository.DeleteAsync(id); 
        }

        

        public async Task<DisasterTypeDto?> GetByIdAsync(int id)
        {
            var disasterType = await _repository.GetByIdAsync(id);
            return disasterType ==null?null :new DisasterTypeDto
            {
                Id = disasterType.Id,
                Name = disasterType.Name,
                IconName = disasterType.IconName,
                OrganizationId = disasterType.OrganizationId,
                Category = disasterType.Category
            };
        }

        public async Task<DisasterTypeDto?> UpdateAsync(int id, UpdateDisasterTypeDto updateDto)
        {
           var existing =await _repository.GetByIdAsync(id);
            if (existing == null)
            {
                return null; // or throw an exception
            }
            existing.Name = updateDto.Name;
            existing.IconName = updateDto.IconName;
            existing.OrganizationId = updateDto.OrganizationId;
            existing.Category = updateDto.Category;
            await _repository.UpdateAsync(existing);

            return new DisasterTypeDto
            {
                Id = existing.Id,
                Name = existing.Name,
                IconName = existing.IconName,
                OrganizationId = existing.OrganizationId,
                Category = existing.Category
            };
        }
    }
}
