﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public class DisasterEventService : IDisasterEventService
    {
        private readonly IDisasterEventRepository _repository;
        public DisasterEventService(IDisasterEventRepository repository)
        {
            _repository = repository;
        }
        public async Task<DisasterEventDto> CreateAsync(CreateDisasterEventDto dto)
        {
            // Check if an event with the same name already exists
            var existing = await _repository.FindByNameAsync(dto.Name);
            if (existing != null)
            {
                throw new InvalidOperationException("A disaster event with the same name already exists.");
            }

            var entity = new DisasterEvent
            {
                Id = Guid.NewGuid(),
                Name = dto.Name,
                DisasterTypeId = dto.DisasterTypeId
            };

            await _repository.AddAsync(entity);

           

            return new DisasterEventDto
            {
                Id = entity.Id,
                Name = entity.Name,
                DisasterTypeId = entity.DisasterTypeId,
                
            };
        }

        public async Task DeleteAsync(Guid id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<IEnumerable<DisasterEventDto>> GetAllAsync()
        {
            var events = await _repository.GetAllAsync();
            return events.Select(e => new DisasterEventDto
            {
                Id = e.Id,
                Name = e.Name,
                DisasterTypeId = e.DisasterTypeId,
                DisasterTypeName = e.DisasterType.Name
            });
        }

        public async Task<DisasterEventDto?> GetByIdAsync(Guid id)
        {
            var e = await _repository.GetByIdAsync(id);
            return e == null ? null : new DisasterEventDto
            {
                Id = e.Id,
                Name = e.Name,
                DisasterTypeId = e.DisasterTypeId,
                DisasterTypeName = e.DisasterType.Name
            };
        }

        public async Task UpdateAsync(Guid id ,UpdateDisasterEventDto dto)
        {
            var existing = await _repository.GetByIdAsync(id);
            if (existing == null)
            {
                throw new Exception($"DisasterEvent with ID {id} not found.");
            }

            // Update fields
            existing.Name = dto.Name;
            existing.DisasterTypeId = dto.DisasterTypeId;

            await _repository.UpdateAsync(id, existing);
        }
    }
}
