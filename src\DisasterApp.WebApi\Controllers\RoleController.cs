using DisasterApp.Application.Services.Interfaces;
using DisasterApp.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace DisasterApp.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RoleController : ControllerBase
{
    private readonly IRoleService _roleService;
    private readonly ILogger<RoleController> _logger;

    public RoleController(IRoleService roleService, ILogger<RoleController> logger)
    {
        _roleService = roleService;
        _logger = logger;
    }

    [HttpGet]
    [AdminOnly]
    public async Task<IActionResult> GetAllRoles()
    {
        try
        {
            var roles = await _roleService.GetAllRolesAsync();
            return Ok(roles.Select(r => new { r.RoleId, r.Name }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving roles");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("my-roles")]
    public async Task<IActionResult> GetMyRoles()
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return BadRequest("Invalid user ID");
            }

            var roles = await _roleService.GetUserRolesAsync(userId);
            return Ok(roles.Select(r => new { r.RoleId, r.Name }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user roles");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("assign")]
    [AdminOnly]
    public async Task<IActionResult> AssignRole([FromBody] AssignRoleRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _roleService.AssignRoleToUserAsync(request.UserId, request.RoleName);
            return Ok(new { message = $"Role '{request.RoleName}' assigned to user successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleName} to user {UserId}", request.RoleName, request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("assign-default")]
    [AdminOnly]
    public async Task<IActionResult> AssignDefaultRole([FromBody] AssignDefaultRoleRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _roleService.AssignDefaultRoleToUserAsync(request.UserId);
            return Ok(new { message = "Default role (user) assigned to user successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning default role to user {UserId}", request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("remove")]
    [AdminOnly]
    public async Task<IActionResult> RemoveRole([FromBody] RemoveRoleRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _roleService.RemoveRoleFromUserAsync(request.UserId, request.RoleName);
            return Ok(new { message = $"Role '{request.RoleName}' removed from user successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleName} from user {UserId}", request.RoleName, request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("user/{userId}/roles")]
    [AdminOrCj]
    public async Task<IActionResult> GetUserRoles(Guid userId)
    {
        try
        {
            var roles = await _roleService.GetUserRolesAsync(userId);
            return Ok(roles.Select(r => new { r.RoleId, r.Name }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving roles for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("user/{userId}/has-role/{roleName}")]
    [AdminOrCj]
    public async Task<IActionResult> CheckUserRole(Guid userId, string roleName)
    {
        try
        {
            var hasRole = await _roleService.UserHasRoleAsync(userId, roleName);
            return Ok(new { userId, roleName, hasRole });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId}", roleName, userId);
            return StatusCode(500, "Internal server error");
        }
    }
}

public class AssignRoleRequest
{
    public Guid UserId { get; set; }
    public string RoleName { get; set; } = null!;
}

public class AssignDefaultRoleRequest
{
    public Guid UserId { get; set; }
}

public class RemoveRoleRequest
{
    public Guid UserId { get; set; }
    public string RoleName { get; set; } = null!;
}