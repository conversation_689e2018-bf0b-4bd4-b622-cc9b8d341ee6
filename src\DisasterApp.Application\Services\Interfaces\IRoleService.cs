using DisasterApp.Domain.Entities;

namespace DisasterApp.Application.Services.Interfaces;

public interface IRoleService
{
    Task<IEnumerable<Role>> GetAllRolesAsync();
    Task<Role?> GetRoleByNameAsync(string roleName);
    Task<Role?> GetDefaultRoleAsync();
    Task AssignRoleToUserAsync(Guid userId, string roleName);
    Task AssignDefaultRoleToUserAsync(Guid userId);
    Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId);
    Task<bool> UserHasRoleAsync(Guid userId, string roleName);
    Task RemoveRoleFromUserAsync(Guid userId, string roleName);
}