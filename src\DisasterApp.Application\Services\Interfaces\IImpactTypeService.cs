﻿using DisasterApp.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IImpactTypeService
    {
        Task<IEnumerable<ImpactTypeDto>> GetAllAsync();
        Task<ImpactTypeDto?> GetByIdAsync(int id);
        Task CreateAsync(CreateImactTypeDto dto);
        Task UpdateAsync(int id, UpdateImpactTypeDto dto);
        Task DeleteAsync(int id);

    }
}
