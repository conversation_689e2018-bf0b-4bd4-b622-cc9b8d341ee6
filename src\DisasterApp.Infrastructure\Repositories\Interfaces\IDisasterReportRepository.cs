﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IDisasterReportRepository
    {
        Task<IEnumerable<DisasterReport>> GetAllAsync();
        Task<DisasterReport?> GetByIdAsync(Guid id);
        Task<DisasterReport> AddAsync(DisasterReport report);
        Task UpdateAsync(DisasterReport report);
        Task DeleteAsync(Guid id);
    }
}
