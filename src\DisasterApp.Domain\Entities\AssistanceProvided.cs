﻿using System;
using System.Collections.Generic;

namespace DisasterApp.Domain.Entities;

public partial class AssistanceProvided
{
    public Guid AssistanceId { get; set; }

    public int RequestId { get; set; }

    public int QuantityProvided { get; set; }

    public DateTime? ProvidedAt { get; set; }

    public DateTime? CompletedAt { get; set; }

    public byte Status { get; set; }

    public string? Notes { get; set; }

    public Guid ProviderId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public virtual User Provider { get; set; } = null!;

    public virtual SupportRequest Request { get; set; } = null!;
}
