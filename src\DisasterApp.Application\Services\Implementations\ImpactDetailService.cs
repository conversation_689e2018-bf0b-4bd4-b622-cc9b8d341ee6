﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public class ImpactDetailService : IImpactDetailService
    {
        private readonly IImpactDetailRepository _repository;
        public ImpactDetailService(IImpactDetailRepository repository)
        {
            _repository = repository;
        }
        public async Task<ImpactDetailDto> CreateAsync(CreateImpactDetailDto impactDetailDto)
        {
            var entity = new ImpactDetail
            {
                ReportId = impactDetailDto.ReportId,
                Description = impactDetailDto.Description,
                Severity = impactDetailDto.Severity,
                IsResolved = impactDetailDto.IsResolved,
                ResolvedAt = impactDetailDto.ResolvedAt,
                ImpactTypeId = impactDetailDto.ImpactTypeId
            };
            await _repository.AddAsync(entity);
            // ပြန်လည်ခေါ်ယူတာ navigation property ဖြစ်တဲ့ ImpactType ကိုထည့်မလို့
            var savedEntity = await _repository.GetByIdAsync(entity.Id);

            if (savedEntity == null)
                throw new Exception("သိမ်းဆည်းပြီးတဲ့ impact detail ကို ပြန်မရနိုင်ပါ။");

            return new ImpactDetailDto
            {
                Id = savedEntity.Id,
                ReportId = savedEntity.ReportId,
                Description = savedEntity.Description,
                Severity = savedEntity.Severity,
                IsResolved = savedEntity.IsResolved,
                ResolvedAt = savedEntity.ResolvedAt,
                ImpactTypeId = savedEntity.ImpactTypeId,
                ImpactTypeName = savedEntity.ImpactType.Name
            };

        }

        public async Task<bool> DeleteAsync(int id)
        {
            var existing = await _repository.GetByIdAsync(id);
            if (existing == null)
                return false;

            await _repository.DeleteAsync(id);
            return true;
        }

        public async Task<IEnumerable<ImpactDetailDto>> GetAllAsync()
        {
            var impactDetails = await _repository.GetAllAsync();
            return impactDetails.Select(impactDetails => new ImpactDetailDto
            {
                Id = impactDetails.Id,
                ReportId = impactDetails.ReportId,
                Description = impactDetails.Description,
                Severity = impactDetails.Severity,
                IsResolved = impactDetails.IsResolved,
                ResolvedAt = impactDetails.ResolvedAt,
                ImpactTypeId= impactDetails.ImpactTypeId, // Assuming ImpactTypeId is a property of ImpactDetail
                ImpactTypeName = impactDetails.ImpactType.Name // Assuming ImpactType is a navigation property
            });
        }

        public async Task<ImpactDetailDto?> GetByIdAsync(int id)
        {
           var i=await _repository.GetByIdAsync(id);
            return i==null?null:new ImpactDetailDto
            {
                Id = i.Id,
                ReportId = i.ReportId,
                Description = i.Description,
                Severity = i.Severity,
                IsResolved = i.IsResolved,
                ResolvedAt = i.ResolvedAt,
                ImpactTypeId = i.ImpactTypeId, // Assuming ImpactTypeId is a property of ImpactDetail
                ImpactTypeName = i.ImpactType.Name // Assuming ImpactType is a navigation property
            };
        }

        public async Task<ImpactDetailDto> UpdateAsync(int id, UpdateImpactDetailDto dto)
        {
            var existing = await _repository.GetByIdAsync(id);
            if (existing == null)
                throw new Exception("ImpactDetail not found.");

            // Update properties
            existing.ReportId = dto.ReportId;
            existing.Description = dto.Description;
            existing.Severity = dto.Severity;
            existing.IsResolved = dto.IsResolved;
            existing.ResolvedAt = dto.ResolvedAt;
            existing.ImpactTypeId = dto.ImpactTypeId;

            await _repository.UpdateAsync(existing);

            return new ImpactDetailDto
            {
                Id = existing.Id,
                ReportId = existing.ReportId,
                Description = existing.Description,
                Severity = existing.Severity,
                IsResolved = existing.IsResolved,
                ResolvedAt = existing.ResolvedAt,
                ImpactTypeId = existing.ImpactTypeId,
                ImpactTypeName = existing.ImpactType.Name
            };
        }
    }
}
