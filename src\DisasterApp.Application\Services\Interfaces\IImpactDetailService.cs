﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IImpactDetailService
    {
        
        Task<IEnumerable<ImpactDetailDto>> GetAllAsync();
        Task<ImpactDetailDto> GetByIdAsync(int id);
        Task<ImpactDetailDto> CreateAsync(CreateImpactDetailDto impactDetailDto);
        Task<ImpactDetailDto> UpdateAsync(int id,UpdateImpactDetailDto impactDetailDto);
        Task<bool> DeleteAsync(int id);
    }
}
