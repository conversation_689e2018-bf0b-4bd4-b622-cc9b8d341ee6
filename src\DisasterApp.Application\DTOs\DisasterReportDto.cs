﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.DTOs
{
    public class DisasterReportDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public DateTime Timestamp { get; set; }
        public byte Severity { get; set; }
        public byte Status { get; set; }
        public int? EstimatedAffected { get; set; }
        public Guid? VerifiedBy { get; set; }
        public DateTime? VerifiedAt { get; set; }
        public bool? IsDeleted { get; set; }
        public Guid UserId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid DisasterEventId { get; set; }
    }
    public class CreateDisasterReportDto
    {
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public DateTime Timestamp { get; set; }
        public byte Severity { get; set; }
        public byte Status { get; set; }
        public int? EstimatedAffected { get; set; }
        public Guid UserId { get; set; }
        public Guid DisasterEventId { get; set; }
    }
   
}


