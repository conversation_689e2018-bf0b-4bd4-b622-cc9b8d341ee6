﻿using DisasterApp.Domain.Enums;
using System;
using System.Collections.Generic;

namespace DisasterApp.Domain.Entities;

public partial class DisasterType
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? IconName { get; set; }

    public int? OrganizationId { get; set; }

    public DisasterCategory Category { get; set; } 

    public virtual ICollection<DisasterEvent> DisasterEvents { get; set; } = new List<DisasterEvent>();

    public virtual Organization? Organization { get; set; }
}
