﻿using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Data;
using DisasterApp.Infrastructure.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public class ImpactDetailRepository : IImpactDetailRepository
    {
        private readonly DisasterDbContext _context;
        public ImpactDetailRepository(DisasterDbContext context)
        {
            _context = context;
        }
        public async Task AddAsync(ImpactDetail impactDetail)
        {
            _context.ImpactDetails.Add(impactDetail);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var impact = await _context.ImpactDetails.FindAsync(id);
            if (impact != null)
            {
                _context.ImpactDetails.Remove(impact);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<ImpactDetail>> GetAllAsync()
        {
            return await _context.ImpactDetails.Include(i => i.ImpactType).ToListAsync();
        }

        public async Task<ImpactDetail?> GetByIdAsync(int id)
        {
            return await _context.ImpactDetails.Include(i => i.ImpactType).FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task UpdateAsync(ImpactDetail entity)
        {
            _context.ImpactDetails.Update(entity);
            await _context.SaveChangesAsync();
        }
    }
}
