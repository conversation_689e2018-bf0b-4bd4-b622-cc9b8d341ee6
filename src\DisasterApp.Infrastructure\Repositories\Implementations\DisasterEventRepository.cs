﻿using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Data;
using DisasterApp.Infrastructure.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public class DisasterEventRepository : IDisasterEventRepository
    {
        private readonly DisasterDbContext _context;
        public DisasterEventRepository(DisasterDbContext context)
        {
            _context = context;
        }
        public async Task<IEnumerable<DisasterEvent>> GetAllAsync()
        {
            return await _context.DisasterEvents.Include(e => e.DisasterType).ToListAsync();
        }


        public async Task<DisasterEvent?> GetByIdAsync(Guid id)
        {
            return await _context.DisasterEvents.Include(e => e.DisasterType).FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task AddAsync(DisasterEvent disasterEvent)
        {
            await _context.DisasterEvents.AddAsync(disasterEvent);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var entity = await _context.DisasterEvents.FindAsync(id);
            if (entity == null) return false;

            _context.DisasterEvents.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        

        public async Task UpdateAsync(Guid id,DisasterEvent disasterEvent)
        {
            _context.DisasterEvents.Update(disasterEvent);
            await _context.SaveChangesAsync();
        }

        public async Task<DisasterEvent?> FindByNameAsync(string name)
        {
            return await _context.DisasterEvents
                                 .FirstOrDefaultAsync(e => e.Name.ToLower() == name.ToLower());
        }
    }
}
